import React, { useState } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useThemedStyles, useCart, useAuth, useData, useI18n } from '../../hooks';
import { Card, Button, Input } from '../../components';
import { RTLView, RTLText, RTLIcon } from '../../components/RTL';
import PaymentService from '../../services/PaymentService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { CURRENCY } from '../../constants';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Address } from '../../contexts/DataContext';

interface CheckoutScreenProps {
  navigation: any;
}

interface CheckoutFormData {
  shippingAddress: Address;
  paymentMethod: 'credit_card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer' | 'buy_now_pay_later';
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

export const CheckoutScreen: React.FC<CheckoutScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { cartItems, cartSummary, clearCart, validateCart } = useCart();
  const { user } = useAuth();
  const { createOrder } = useData();
  const { t } = useI18n();
  
  const [currentStep, setCurrentStep] = useState<'shipping' | 'payment' | 'review'>('shipping');
  const [isLoading, setIsLoading] = useState(false);
  
  const [formData, setFormData] = useState<CheckoutFormData>({
    shippingAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA',
    },
    paymentMethod: 'credit_card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  });

  const [errors, setErrors] = useState<any>({});

  const validateShippingAddress = (): boolean => {
    const newErrors: any = {};
    const { shippingAddress } = formData;

    if (!shippingAddress.street.trim()) {
      newErrors.street = 'Street address is required';
    }
    if (!shippingAddress.city.trim()) {
      newErrors.city = 'City is required';
    }
    if (!shippingAddress.state.trim()) {
      newErrors.state = 'State is required';
    }
    if (!shippingAddress.zipCode.trim()) {
      newErrors.zipCode = 'ZIP code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePaymentInfo = (): boolean => {
    if (formData.paymentMethod !== 'credit_card') {
      return true; // Other payment methods would be handled by external services
    }

    const newErrors: any = {};

    if (!formData.cardNumber.trim()) {
      newErrors.cardNumber = 'Card number is required';
    } else if (formData.cardNumber.replace(/\s/g, '').length < 16) {
      newErrors.cardNumber = 'Please enter a valid card number';
    }

    if (!formData.expiryDate.trim()) {
      newErrors.expiryDate = 'Expiry date is required';
    } else if (!/^\d{2}\/\d{2}$/.test(formData.expiryDate)) {
      newErrors.expiryDate = 'Please enter date in MM/YY format';
    }

    if (!formData.cvv.trim()) {
      newErrors.cvv = 'CVV is required';
    } else if (formData.cvv.length < 3) {
      newErrors.cvv = 'Please enter a valid CVV';
    }

    if (!formData.cardholderName.trim()) {
      newErrors.cardholderName = 'Cardholder name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('shippingAddress.')) {
      const addressField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        shippingAddress: {
          ...prev.shippingAddress,
          [addressField]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when user starts typing
    if (errors[field] || errors[field.split('.')[1]]) {
      setErrors((prev: any) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        delete newErrors[field.split('.')[1]];
        return newErrors;
      });
    }
  };

  const handleNextStep = () => {
    if (currentStep === 'shipping') {
      if (validateShippingAddress()) {
        setCurrentStep('payment');
      }
    } else if (currentStep === 'payment') {
      if (validatePaymentInfo()) {
        setCurrentStep('review');
      }
    }
  };

  const handlePreviousStep = () => {
    if (currentStep === 'payment') {
      setCurrentStep('shipping');
    } else if (currentStep === 'review') {
      setCurrentStep('payment');
    }
  };

  const handlePlaceOrder = async () => {
    if (!user) {
      Alert.alert(t('common.error'), t('checkout.loginRequired'));
      return;
    }

    // Validate cart before placing order
    const cartValidation = validateCart();
    if (!cartValidation.isValid) {
      Alert.alert(
        t('checkout.cartIssues'),
        t('checkout.cartValidationError'),
        [{ text: t('common.confirm'), onPress: () => navigation.navigate('Cart') }]
      );
      return;
    }

    // Validate shipping and payment info
    if (!validateShippingAddress() || !validatePaymentInfo()) {
      Alert.alert(t('common.error'), t('checkout.completeRequiredFields'));
      return;
    }

    setIsLoading(true);
    try {
      // Process payment first
      const paymentMethod = {
        type: formData.paymentMethod,
        cardNumber: formData.cardNumber,
        expiryDate: formData.expiryDate,
        cvv: formData.cvv,
        cardholderName: formData.cardholderName,
      };

      const paymentIntent = await PaymentService.createPaymentIntent(
        cartSummary.total,
        CURRENCY.CODE.toLowerCase(),
        {
          orderId: Date.now().toString(),
          customerId: user.id,
          customerEmail: user.email,
        }
      );

      const paymentResult = await PaymentService.processPayment(paymentMethod, paymentIntent);

      if (!paymentResult.success) {
        Alert.alert(t('common.error'), t('checkout.paymentFailed'));
        return;
      }
      // Create order items from cart
      const orderItems = cartItems.map(item => ({
        id: 'order-item-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
        productId: item.productId,
        vendorId: item.vendorId,
        productName: item.product.name,
        productImage: item.product.images[0] || '',
        quantity: item.quantity,
        price: item.product.price,
        totalPrice: item.product.price * item.quantity,
      }));

      const orderData = {
        customerId: user.id,
        customerName: user.name,
        customerEmail: user.email,
        items: orderItems,
        totalAmount: cartSummary.total,
        status: 'confirmed' as const,
        shippingAddress: formData.shippingAddress,
        paymentMethod: formData.paymentMethod,
        paymentId: paymentResult.paymentId,
      };

      await createOrder(orderData);
      clearCart();

      Alert.alert(
        t('checkout.orderPlaced'),
        t('checkout.thankYou'),
        [
          {
            text: t('orders.orderHistory'),
            onPress: () => navigation.navigate('OrderHistory'),
          },
          {
            text: t('checkout.continueShoppingAfterOrder'),
            onPress: () => navigation.navigate('Home'),
          },
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('common.tryAgain'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const renderStepIndicator = () => (
    <RTLView style={styles.stepIndicator}>
      <RTLView style={styles.stepContainer}>
        <RTLView style={[styles.step, currentStep === 'shipping' && styles.activeStep]}>
          <RTLText style={[styles.stepNumber, currentStep === 'shipping' && styles.activeStepNumber]}>1</RTLText>
        </RTLView>
        <RTLText style={styles.stepLabel}>{t('cart.shipping')}</RTLText>
      </RTLView>

      <RTLView style={styles.stepLine} />

      <RTLView style={styles.stepContainer}>
        <RTLView style={[styles.step, currentStep === 'payment' && styles.activeStep]}>
          <RTLText style={[styles.stepNumber, currentStep === 'payment' && styles.activeStepNumber]}>2</RTLText>
        </RTLView>
        <RTLText style={styles.stepLabel}>{t('checkout.paymentInformation')}</RTLText>
      </RTLView>

      <RTLView style={styles.stepLine} />

      <RTLView style={styles.stepContainer}>
        <RTLView style={[styles.step, currentStep === 'review' && styles.activeStep]}>
          <RTLText style={[styles.stepNumber, currentStep === 'review' && styles.activeStepNumber]}>3</RTLText>
        </RTLView>
        <RTLText style={styles.stepLabel}>{t('checkout.review')}</RTLText>
      </RTLView>
    </RTLView>
  );

  const renderShippingStep = () => (
    <Card style={styles.stepCard} variant="outlined">
      <RTLText style={styles.stepTitle}>{t('checkout.shippingAddress')}</RTLText>

      <Input
        label={`${t('checkout.streetAddress')} *`}
        placeholder="123 Main Street"
        value={formData.shippingAddress.street}
        onChangeText={(value) => handleInputChange('shippingAddress.street', value)}
        error={errors.street}
        style={styles.input}
      />

      <RTLView style={styles.row}>
        <Input
          label={`${t('checkout.city')} *`}
          placeholder="New York"
          value={formData.shippingAddress.city}
          onChangeText={(value) => handleInputChange('shippingAddress.city', value)}
          error={errors.city}
          style={[styles.input, styles.halfInput]}
        />
        <Input
          label={`${t('checkout.state')} *`}
          placeholder="NY"
          value={formData.shippingAddress.state}
          onChangeText={(value) => handleInputChange('shippingAddress.state', value)}
          error={errors.state}
          style={[styles.input, styles.halfInput]}
        />
      </RTLView>

      <RTLView style={styles.row}>
        <Input
          label={`${t('checkout.zipCode')} *`}
          placeholder="10001"
          value={formData.shippingAddress.zipCode}
          onChangeText={(value) => handleInputChange('shippingAddress.zipCode', value)}
          error={errors.zipCode}
          keyboardType="numeric"
          style={[styles.input, styles.halfInput]}
        />
        <Input
          label={t('checkout.country')}
          value={formData.shippingAddress.country}
          editable={false}
          style={[styles.input, styles.halfInput]}
        />
      </RTLView>
    </Card>
  );

  const renderPaymentStep = () => (
    <Card style={styles.stepCard} variant="outlined">
      <RTLText style={styles.stepTitle}>{t('checkout.paymentInformation')}</RTLText>

      {/* Payment Method Selection */}
      <RTLText style={styles.sectionLabel}>{t('checkout.paymentMethod')}</RTLText>
      <RTLView style={styles.paymentMethods}>
        {[
          { id: 'credit_card', label: t('checkout.creditCard'), icon: 'card-outline' },
          { id: 'paypal', label: t('checkout.paypal'), icon: 'logo-paypal' },
          { id: 'apple_pay', label: t('checkout.applePay'), icon: 'logo-apple' },
          { id: 'google_pay', label: t('checkout.googlePay'), icon: 'logo-google' },
          { id: 'bank_transfer', label: t('checkout.bankTransfer'), icon: 'business-outline' },
          { id: 'buy_now_pay_later', label: t('checkout.buyNowPayLater'), icon: 'time-outline' },
        ].map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethod,
              formData.paymentMethod === method.id && styles.selectedPaymentMethod,
            ]}
            onPress={() => handleInputChange('paymentMethod', method.id)}
          >
            <RTLIcon name={method.icon as any} size={24} color="#667eea" />
            <RTLText style={styles.paymentMethodText}>{method.label}</RTLText>
            {formData.paymentMethod === method.id && (
              <RTLIcon name="checkmark-circle" size={20} color="#4CAF50" />
            )}
          </TouchableOpacity>
        ))}
      </RTLView>

      {/* Credit Card Form */}
      {formData.paymentMethod === 'credit_card' && (
        <>
          <Input
            label={`${t('checkout.cardNumber')} *`}
            placeholder="1234 5678 9012 3456"
            value={formData.cardNumber}
            onChangeText={(value) => handleInputChange('cardNumber', formatCardNumber(value))}
            error={errors.cardNumber}
            keyboardType="numeric"
            maxLength={19}
            style={styles.input}
          />

          <RTLView style={styles.row}>
            <Input
              label={`${t('checkout.expiryDate')} *`}
              placeholder="MM/YY"
              value={formData.expiryDate}
              onChangeText={(value) => handleInputChange('expiryDate', formatExpiryDate(value))}
              error={errors.expiryDate}
              keyboardType="numeric"
              maxLength={5}
              style={[styles.input, styles.halfInput]}
            />
            <Input
              label={`${t('checkout.cvv')} *`}
              placeholder="123"
              value={formData.cvv}
              onChangeText={(value) => handleInputChange('cvv', value)}
              error={errors.cvv}
              keyboardType="numeric"
              maxLength={4}
              secureTextEntry
              style={[styles.input, styles.halfInput]}
            />
          </RTLView>

          <Input
            label={`${t('checkout.cardholderName')} *`}
            placeholder="John Doe"
            value={formData.cardholderName}
            onChangeText={(value) => handleInputChange('cardholderName', value)}
            error={errors.cardholderName}
            style={styles.input}
          />
        </>
      )}
    </Card>
  );

  const renderReviewStep = () => (
    <>
      {/* Order Summary */}
      <Card style={styles.stepCard} variant="outlined">
        <RTLText style={styles.stepTitle}>{t('cart.orderSummary')}</RTLText>

        {cartItems.map((item) => (
          <RTLView key={item.id} style={styles.orderItem}>
            <RTLView style={styles.orderItemImage}>
              <RTLIcon name="image-outline" size={24} color="#CCCCCC" />
            </RTLView>
            <RTLView style={styles.orderItemInfo}>
              <RTLText style={styles.orderItemName}>{item.product.name}</RTLText>
              <RTLText style={styles.orderItemDetails}>
                {t('cart.quantity')}: {item.quantity} × {formatCurrency(item.product.price)}
              </RTLText>
            </RTLView>
            <RTLText style={styles.orderItemTotal}>
              {formatCurrency(item.product.price * item.quantity)}
            </RTLText>
          </RTLView>
        ))}
      </Card>

      {/* Shipping Address */}
      <Card style={styles.stepCard} variant="outlined">
        <RTLText style={styles.stepTitle}>{t('checkout.shippingAddress')}</RTLText>
        <RTLText style={styles.addressText}>
          {formData.shippingAddress.street}{'\n'}
          {formData.shippingAddress.city}, {formData.shippingAddress.state} {formData.shippingAddress.zipCode}{'\n'}
          {formData.shippingAddress.country}
        </RTLText>
        <TouchableOpacity onPress={() => setCurrentStep('shipping')}>
          <RTLText style={styles.editText}>{t('checkout.edit')}</RTLText>
        </TouchableOpacity>
      </Card>

      {/* Payment Method */}
      <Card style={styles.stepCard} variant="outlined">
        <RTLText style={styles.stepTitle}>{t('checkout.paymentMethod')}</RTLText>
        <RTLView style={styles.paymentSummary}>
          <RTLIcon
            name={formData.paymentMethod === 'credit_card' ? 'card-outline' : 'logo-paypal'}
            size={24}
            color="#667eea"
          />
          <RTLText style={styles.paymentSummaryText}>
            {formData.paymentMethod === 'credit_card'
              ? `**** **** **** ${formData.cardNumber.slice(-4)}`
              : formData.paymentMethod.replace('_', ' ').toUpperCase()
            }
          </RTLText>
        </RTLView>
        <TouchableOpacity onPress={() => setCurrentStep('payment')}>
          <RTLText style={styles.editText}>{t('checkout.edit')}</RTLText>
        </TouchableOpacity>
      </Card>

      {/* Order Total */}
      <Card style={styles.stepCard} variant="outlined">
        <RTLText style={styles.stepTitle}>{t('cart.total')}</RTLText>

        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>{t('cart.subtotal')}</RTLText>
          <RTLText style={styles.summaryValue}>{formatCurrency(cartSummary.subtotal)}</RTLText>
        </RTLView>

        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>{t('cart.tax')}</RTLText>
          <RTLText style={styles.summaryValue}>{formatCurrency(cartSummary.tax)}</RTLText>
        </RTLView>

        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>{t('cart.shipping')}</RTLText>
          <RTLText style={styles.summaryValue}>
            {cartSummary.shipping === 0 ? t('cart.free') : formatCurrency(cartSummary.shipping)}
          </RTLText>
        </RTLView>

        <RTLView style={[styles.summaryRow, styles.totalRow]}>
          <RTLText style={styles.totalLabel}>{t('cart.total')}</RTLText>
          <RTLText style={styles.totalValue}>{formatCurrency(cartSummary.total)}</RTLText>
        </RTLView>
      </Card>
    </>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 'shipping':
        return renderShippingStep();
      case 'payment':
        return renderPaymentStep();
      case 'review':
        return renderReviewStep();
      default:
        return null;
    }
  };

  const renderActionButtons = () => (
    <RTLView style={styles.actionButtons}>
      {currentStep !== 'shipping' && (
        <Button
          title={t('common.back')}
          onPress={handlePreviousStep}
          variant="outline"
          style={styles.backButton}
          leftIcon={<RTLIcon name="chevron-back" size={20} color="#667eea" />}
        />
      )}

      {currentStep !== 'review' ? (
        <Button
          title={t('common.next')}
          onPress={handleNextStep}
          style={styles.continueButton}
          rightIcon={<RTLIcon name="chevron-forward" size={20} color="#FFFFFF" />}
        />
      ) : (
        <Button
          title={t('checkout.placeOrder')}
          onPress={handlePlaceOrder}
          loading={isLoading}
          style={styles.placeOrderButton}
          leftIcon={<RTLIcon name="checkmark-outline" size={20} color="#FFFFFF" />}
        />
      )}
    </RTLView>
  );

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <RTLView style={styles.emptyContainer}>
          <RTLIcon name="bag-outline" size={64} color="#CCCCCC" />
          <RTLText style={styles.emptyTitle}>{t('checkout.emptyCartTitle')}</RTLText>
          <RTLText style={styles.emptyDescription}>
            {t('checkout.emptyCartDescription')}
          </RTLText>
          <Button
            title={t('cart.startShopping')}
            onPress={() => navigation.navigate('Home')}
            style={styles.emptyButton}
          />
        </RTLView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <RTLView style={styles.content}>
            {/* Step Indicator */}
            {renderStepIndicator()}

            {/* Step Content */}
            {renderStepContent()}
          </RTLView>
        </ScrollView>

        {/* Action Buttons */}
        {renderActionButtons()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: SPACING.lg,
      paddingBottom: 100, // Space for action buttons
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    emptyTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginTop: SPACING.md,
      marginBottom: SPACING.sm,
    },
    emptyDescription: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: SPACING.xl,
    },
    emptyButton: {
      minWidth: 150,
    },
    stepIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: SPACING.xl,
    },
    stepContainer: {
      alignItems: 'center',
    },
    step: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.surface,
      borderWidth: 2,
      borderColor: colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    activeStep: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    stepNumber: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textSecondary,
    },
    activeStepNumber: {
      color: '#FFFFFF',
    },
    stepLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    stepLine: {
      width: 40,
      height: 2,
      backgroundColor: colors.border,
      marginHorizontal: SPACING.sm,
    },
    stepCard: {
      marginBottom: SPACING.lg,
    },
    stepTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    input: {
      marginBottom: SPACING.md,
    },
    row: {
      flexDirection: 'row',
      gap: SPACING.md,
    },
    halfInput: {
      flex: 1,
    },
    sectionLabel: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    paymentMethods: {
      marginBottom: SPACING.lg,
    },
    paymentMethod: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
      marginBottom: SPACING.sm,
    },
    selectedPaymentMethod: {
      borderColor: '#667eea',
      backgroundColor: 'rgba(102, 126, 234, 0.1)',
    },
    paymentMethodText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
      flex: 1,
    },
    orderItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    orderItemImage: {
      width: 40,
      height: 40,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    orderItemInfo: {
      flex: 1,
    },
    orderItemName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    orderItemDetails: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    orderItemTotal: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    addressText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      lineHeight: 22,
      marginBottom: SPACING.sm,
    },
    editText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.semibold,
    },
    paymentSummary: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    paymentSummaryText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.xs,
    },
    summaryLabel: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
    },
    summaryValue: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    totalRow: {
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingTop: SPACING.sm,
      marginTop: SPACING.sm,
    },
    totalLabel: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    totalValue: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
    },
    actionButtons: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      gap: SPACING.md,
      padding: SPACING.lg,
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    backButton: {
      flex: 1,
    },
    continueButton: {
      flex: 2,
    },
    placeOrderButton: {
      flex: 1,
    },
  });
