import React, { useState } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { Card, Button, Input, EmptyState, StatusBadge, ChatButton, RTLView, RTLText, RTLScrollView, RTLSafeAreaView, RTLFlatList, RTLIcon } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Product } from '../../contexts/DataContext';

interface VendorShopScreenProps {
  navigation: any;
  route: {
    params: {
      vendorId: string;
    };
  };
}

const { width: screenWidth } = Dimensions.get('window');
const PRODUCT_CARD_WIDTH = (screenWidth - SPACING.lg * 3) / 2;

export const VendorShopScreen: React.FC<VendorShopScreenProps> = ({ navigation, route }) => {
  const { vendorId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { getVendorById } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const vendor = getVendorById(vendorId);
  const allProducts = getProductsByVendor(vendorId);
  const activeProducts = allProducts.filter(product => product.isActive);

  // Get unique categories from vendor's products
  const categories = React.useMemo(() => {
    const cats = [...new Set(activeProducts.map(product => product.category))];
    return cats.sort();
  }, [activeProducts]);

  // Filter products based on search and category
  const filteredProducts = React.useMemo(() => {
    let filtered = activeProducts;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    return filtered;
  }, [activeProducts, searchQuery, selectedCategory]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetails', { productId });
  };

  if (!vendor) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="storefront-outline" size={64} color="#CCCCCC" />
          <Text style={styles.errorTitle}>Shop Not Found</Text>
          <Text style={styles.errorDescription}>
            The shop you're looking for doesn't exist or is no longer available.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            style={styles.errorButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const renderVendorHeader = () => (
    <Card style={styles.vendorHeader} variant="elevated">
      <View style={styles.vendorInfo}>
        <View style={styles.vendorLogo}>
          <Ionicons name="storefront" size={48} color="#667eea" />
        </View>
        <View style={styles.vendorDetails}>
          <Text style={styles.vendorName}>{vendor.businessName}</Text>
          <Text style={styles.vendorDescription} numberOfLines={2}>
            {vendor.businessDescription}
          </Text>
          <View style={styles.vendorStats}>
            <View style={styles.statItem}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.statText}>{vendor.rating.toFixed(1)}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="cube-outline" size={16} color="#667eea" />
              <Text style={styles.statText}>{activeProducts.length} products</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="location-outline" size={16} color="#667eea" />
              <Text style={styles.statText}>{vendor.address.city}, {vendor.address.state}</Text>
            </View>
          </View>
        </View>
        <View style={styles.vendorActions}>
          <ChatButton
            targetUserId={vendor.id}
            targetUserName={vendor.businessName}
            targetUserAvatar={vendor.businessLogo}
            variant="secondary"
            size="medium"
          />
        </View>
      </View>

      {vendor.businessDescription.length > 100 && (
        <TouchableOpacity style={styles.expandButton}>
          <Text style={styles.expandText}>View Full Description</Text>
          <Ionicons name="chevron-down" size={16} color="#667eea" />
        </TouchableOpacity>
      )}
    </Card>
  );

  const renderSearchAndFilters = () => (
    <View style={styles.searchSection}>
      {/* Search Bar */}
      <Input
        placeholder="Search products..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon={<Ionicons name="search-outline" size={20} color="#CCCCCC" />}
        rightIcon={
          searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#CCCCCC" />
            </TouchableOpacity>
          ) : undefined
        }
        style={styles.searchInput}
      />

      {/* Category Filters */}
      {categories.length > 0 && (
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.categoryChip, !selectedCategory && styles.categoryChipActive]}
              onPress={() => setSelectedCategory(null)}
            >
              <Text style={[styles.categoryChipText, !selectedCategory && styles.categoryChipTextActive]}>
                All ({activeProducts.length})
              </Text>
            </TouchableOpacity>
            {categories.map((category) => {
              const count = activeProducts.filter(p => p.category === category).length;
              return (
                <TouchableOpacity
                  key={category}
                  style={[styles.categoryChip, selectedCategory === category && styles.categoryChipActive]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[styles.categoryChipText, selectedCategory === category && styles.categoryChipTextActive]}>
                    {category} ({count})
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* View Mode Toggle & Results */}
      <View style={styles.controlsRow}>
        <Text style={styles.resultsText}>
          {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
        </Text>
        <View style={styles.viewModeToggle}>
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'grid' && styles.viewModeButtonActive]}
            onPress={() => setViewMode('grid')}
          >
            <Ionicons name="grid-outline" size={20} color={viewMode === 'grid' ? '#FFFFFF' : '#667eea'} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'list' && styles.viewModeButtonActive]}
            onPress={() => setViewMode('list')}
          >
            <Ionicons name="list-outline" size={20} color={viewMode === 'list' ? '#FFFFFF' : '#667eea'} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderProductGrid = ({ item }: { item: Product }) => (
    <TouchableOpacity
      style={styles.productGridCard}
      onPress={() => handleProductPress(item.id)}
    >
      <View style={styles.productImage}>
        <Ionicons name="image-outline" size={32} color="#CCCCCC" />
      </View>
      <View style={styles.productGridInfo}>
        <Text style={styles.productGridName} numberOfLines={2}>
          {item.name}
        </Text>
        <View style={styles.productGridPricing}>
          <Text style={styles.productGridPrice}>{formatCurrency(item.price)}</Text>
          {item.originalPrice && item.originalPrice > item.price && (
            <Text style={styles.productGridOriginalPrice}>
              {formatCurrency(item.originalPrice)}
            </Text>
          )}
        </View>
        <View style={styles.productGridFooter}>
          <View style={styles.productGridRating}>
            <Ionicons name="star" size={12} color="#FFD700" />
            <Text style={styles.productGridRatingText}>{item.rating.toFixed(1)}</Text>
          </View>
          {item.inventory <= 10 && item.inventory > 0 && (
            <StatusBadge status="Low Stock" variant="warning" size="small" />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderProductList = ({ item }: { item: Product }) => (
    <Card style={styles.productListCard} variant="outlined">
      <TouchableOpacity
        style={styles.productListContent}
        onPress={() => handleProductPress(item.id)}
      >
        <RTLView style={styles.productListImage}>
          <RTLIcon name="image-outline" size={40} color="#CCCCCC" />
        </RTLView>
        <RTLView style={styles.productListInfo}>
          <RTLText style={styles.productListName} numberOfLines={1}>
            {item.name}
          </RTLText>
          <RTLText style={styles.productListDescription} numberOfLines={2}>
            {item.description}
          </RTLText>
          <RTLView style={styles.productListPricing}>
            <RTLText style={styles.productListPrice}>{formatCurrency(item.price)}</RTLText>
            {item.originalPrice && item.originalPrice > item.price && (
              <RTLText style={styles.productListOriginalPrice}>
                {formatCurrency(item.originalPrice)}
              </RTLText>
            )}
          </RTLView>
        </RTLView>
        <RTLView style={styles.productListActions}>
          <RTLView style={styles.productListRating}>
            <RTLIcon name="star" size={14} color="#FFD700" />
            <RTLText style={styles.productListRatingText}>{item.rating.toFixed(1)}</RTLText>
          </RTLView>
          <RTLText style={styles.productListStock}>
            {item.inventory > 0 ? `${item.inventory} in stock` : 'Out of stock'}
          </RTLText>
          <Ionicons name="chevron-forward" size={20} color="#CCCCCC" />
        </RTLView>
      </TouchableOpacity>
    </Card>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Vendor Header */}
        {renderVendorHeader()}

        {/* Search and Filters */}
        {renderSearchAndFilters()}

        {/* Products */}
        <RTLView style={styles.productsContainer}>
          {filteredProducts.length > 0 ? (
            <RTLFlatList
              data={filteredProducts}
              renderItem={viewMode === 'grid' ? renderProductGrid : renderProductList}
              keyExtractor={(item) => item.id}
              numColumns={viewMode === 'grid' ? 2 : 1}
              key={viewMode} // Force re-render when view mode changes
              scrollEnabled={false}
              contentContainerStyle={styles.productsList}
              columnWrapperStyle={viewMode === 'grid' ? styles.productRow : undefined}
            />
          ) : (
            <EmptyState
              icon="cube-outline"
              title={t('products.noProductsFound')}
              description={
                searchQuery || selectedCategory
                  ? t('products.adjustSearchFilter')
                  : t('products.noProductsYet')
              }
            />
          )}
        </RTLView>
      </RTLScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    errorTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginTop: SPACING.md,
      marginBottom: SPACING.sm,
    },
    errorDescription: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: SPACING.xl,
    },
    errorButton: {
      minWidth: 120,
    },
    vendorHeader: {
      margin: SPACING.lg,
      marginBottom: SPACING.md,
    },
    vendorInfo: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    vendorLogo: {
      width: 80,
      height: 80,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    vendorDetails: {
      flex: 1,
    },
    vendorActions: {
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: SPACING.md,
    },
    vendorName: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    vendorDescription: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: SPACING.sm,
    },
    vendorStats: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.md,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    expandButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: SPACING.md,
      paddingTop: SPACING.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    expandText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.medium,
      marginRight: SPACING.xs,
    },
    searchSection: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.md,
    },
    searchInput: {
      marginBottom: SPACING.md,
    },
    filtersContainer: {
      marginBottom: SPACING.md,
    },
    categoryChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      marginRight: SPACING.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    categoryChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    categoryChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    categoryChipTextActive: {
      color: '#FFFFFF',
    },
    controlsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    viewModeToggle: {
      flexDirection: 'row',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
      padding: 2,
    },
    viewModeButton: {
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    viewModeButtonActive: {
      backgroundColor: '#667eea',
    },
    productsContainer: {
      paddingHorizontal: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    productsList: {
      paddingTop: SPACING.sm,
    },
    productRow: {
      justifyContent: 'space-between',
    },
    // Grid View Styles
    productGridCard: {
      width: PRODUCT_CARD_WIDTH,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      marginBottom: SPACING.md,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
    },
    productImage: {
      height: 120,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
    },
    productGridInfo: {
      padding: SPACING.sm,
    },
    productGridName: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
      minHeight: 32, // Ensure consistent height
    },
    productGridPricing: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    productGridPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginRight: SPACING.xs,
    },
    productGridOriginalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    productGridFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    productGridRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productGridRatingText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    // List View Styles
    productListCard: {
      marginBottom: SPACING.sm,
    },
    productListContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productListImage: {
      width: 60,
      height: 60,
      backgroundColor: colors.background,
      borderRadius: BORDER_RADIUS.sm,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    productListInfo: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    productListName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    productListDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    productListPricing: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productListPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginRight: SPACING.xs,
    },
    productListOriginalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    productListActions: {
      alignItems: 'flex-end',
    },
    productListRating: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    productListRatingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    productListStock: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
  });
